package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.service.CardCollectionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/card-collections")
public class CardCollectionManagementController {

    private final CardCollectionService cardCollectionService;
    private final CardCollectionMapper mapper;

    public CardCollectionManagementController(CardCollectionService cardCollectionService, 
                                            CardCollectionMapper mapper) {
        this.cardCollectionService = cardCollectionService;
        this.mapper = mapper;
    }

    // === COLLECTION MANAGEMENT ===

    @Operation(description = "Get all card collections with optional status filter", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardCollectionSummaryResponse> getAllCollections(
            @RequestParam(required = false) CardCollection.CollectionStatus status,
            @RequestParam(required = false, defaultValue = "false") boolean activeOnly) {
        List<CardCollection> collections;

        if (activeOnly) {
            collections = cardCollectionService.findActiveCollections();
        } else if (status != null) {
            collections = cardCollectionService.findByStatus(status);
        } else {
            collections = cardCollectionService.findAll();
        }

        return mapper.toSummaryResponseList(collections);
    }

    @Operation(description = "Get card collections with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/paged")
    @RolesAllowed("ADMIN")
    public Page<CardCollectionDto.CardCollectionSummaryResponse> getCollectionsPaged(@PageableDefault(size = 20) Pageable pageable) {
        return cardCollectionService.findAll(pageable)
                .map(mapper::toSummaryResponse);
    }

    @Operation(description = "Get card collection by ID with full details", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> getCollectionById(@PathVariable Integer id) {
        return cardCollectionService.findByIdWithCardsAndRewards(id)
                .map(collection -> ResponseEntity.ok(mapper.toResponse(collection)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create new card collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createCollection(@RequestBody CardCollectionDto.CreateCardCollectionRequest request) {
        CardCollection collection = cardCollectionService.create(request);
        // Перезагружаем с полными данными
        CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
        return mapper.toResponse(fullCollection);
    }

    @Operation(description = "Update card collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> updateCollection(
            @PathVariable Integer id,
            @RequestBody CardCollectionDto.UpdateCardCollectionRequest request) {
        return cardCollectionService.update(id, request)
                .map(collection -> {
                    // Перезагружаем с полными данными
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Delete card collection", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCollection(@PathVariable Integer id) {
        if (cardCollectionService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    // === CARD MANAGEMENT WITHIN COLLECTION ===

    @Operation(description = "Create card in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{collectionId}/cards")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createCard(
            @PathVariable Integer collectionId,
            @RequestBody CardCollectionDto.CreateCardRequest request) {
        CardCollection collection = cardCollectionService.createCard(collectionId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Update card in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse updateCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId,
            @RequestBody CardCollectionDto.UpdateCardRequest request) {
        CardCollection collection = cardCollectionService.updateCard(collectionId, cardId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Delete card from collection", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse deleteCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        CardCollection collection = cardCollectionService.deleteCard(collectionId, cardId);
        return mapper.toResponse(collection);
    }

    // === REWARD MANAGEMENT WITHIN COLLECTION ===

    @Operation(description = "Create reward for collection or card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{collectionId}/rewards")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createReward(
            @PathVariable Integer collectionId,
            @RequestBody CardCollectionDto.CreateRewardRequest request) {
        CardCollection collection = cardCollectionService.createReward(collectionId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Update reward", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/{collectionId}/rewards/{rewardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse updateReward(
            @PathVariable Integer collectionId,
            @PathVariable Integer rewardId,
            @RequestBody CardCollectionDto.UpdateRewardRequest request) {
        CardCollection collection = cardCollectionService.updateReward(collectionId, rewardId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Delete reward", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/{collectionId}/rewards/{rewardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse deleteReward(
            @PathVariable Integer collectionId,
            @PathVariable Integer rewardId) {
        CardCollection collection = cardCollectionService.deleteReward(collectionId, rewardId);
        return mapper.toResponse(collection);
    }

    // === COLLECTION STATUS MANAGEMENT ===

    @Operation(description = "Activate collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/activate")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> activateCollection(@PathVariable Integer id) {
        return cardCollectionService.activate(id)
                .map(collection -> {
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Disable collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/{id}/disable")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> disableCollection(@PathVariable Integer id) {
        return cardCollectionService.disable(id)
                .map(collection -> {
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }
}
