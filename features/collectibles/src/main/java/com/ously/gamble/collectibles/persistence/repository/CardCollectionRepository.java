package com.ously.gamble.collectibles.persistence.repository;

import com.ously.gamble.collectibles.persistence.model.CardCollection;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface CardCollectionRepository extends JpaRepository<CardCollection, Integer> {

    List<CardCollection> findByStatus(CardCollection.CollectionStatus status);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.status = 'ENABLED' " +
           "AND cc.startDate <= :now " +
           "AND (cc.endDate IS NULL OR cc.endDate > :now)")
    List<CardCollection> findActiveCollections(@Param("now") LocalDateTime now);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.status = 'ENABLED' " +
           "AND cc.startDate <= :now " +
           "AND (cc.endDate IS NULL OR cc.endDate > :now)")
    Page<CardCollection> findActiveCollections(@Param("now") LocalDateTime now, Pageable pageable);

    Page<CardCollection> findByStatus(CardCollection.CollectionStatus status, Pageable pageable);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.status = 'EXPIRED' " +
           "OR (cc.endDate IS NOT NULL AND cc.endDate <= :now)")
    List<CardCollection> findExpiredCollections(@Param("now") LocalDateTime now);

    List<CardCollection> findByStartDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    List<CardCollection> findByEndDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    @Query("SELECT cc FROM CardCollection cc WHERE cc.endDate IS NOT NULL " +
           "AND cc.endDate BETWEEN :startDate AND :endDate")
    List<CardCollection> findExpiringBetween(@Param("startDate") LocalDateTime startDate, 
                                           @Param("endDate") LocalDateTime endDate);

    List<CardCollection> findByNameContainingIgnoreCase(String name);

    List<CardCollection> findByOrderBySortOrderAscCreatedAtAsc();

    @Query("SELECT COUNT(cc) FROM CardCollection cc WHERE cc.status = :status")
    long countByStatus(@Param("status") CardCollection.CollectionStatus status);

    @Query("SELECT cc FROM CardCollection cc LEFT JOIN FETCH cc.cards WHERE cc.id = :id")
    CardCollection findByIdWithCards(@Param("id") Integer id);

    @Query("SELECT cc FROM CardCollection cc LEFT JOIN FETCH cc.rewards WHERE cc.id = :id")
    CardCollection findByIdWithRewards(@Param("id") Integer id);

    @Query("SELECT cc FROM CardCollection cc " +
           "LEFT JOIN FETCH cc.cards c " +
           "LEFT JOIN FETCH cc.rewards r " +
           "WHERE cc.id = :id")
    CardCollection findByIdWithCardsAndRewards(@Param("id") Integer id);
}
