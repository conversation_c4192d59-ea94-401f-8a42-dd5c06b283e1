package com.ously.gamble.collectibles.persistence.model;

import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

@Entity
@Table(name = "rewards")
public class Reward {
    public enum RewardType {
        COMPLETION, MILESTONE
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", updatable = false)
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_collection_id")
    private CardCollection cardCollection;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "card_id")
    private Card card;

    @Enumerated(EnumType.STRING)
    @Column(name = "reward_type", nullable = false)
    private RewardType rewardType;

    @Column(name = "milestone_percentage")
    private Byte milestonePercentage;

    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "reward_data", nullable = false, columnDefinition = "JSON")
    private JsonNode rewardData;

    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public Reward() {}

    // Simple utility methods (not business logic)
    public boolean isForCollection() {
        return cardCollection != null && card == null;
    }

    public boolean isForCard() {
        return card != null && cardCollection == null;
    }

    public boolean isCompletionReward() {
        return rewardType == RewardType.COMPLETION;
    }

    public boolean isMilestoneReward() {
        return rewardType == RewardType.MILESTONE;
    }

    public String getTargetName() {
        if (isForCollection()) {
            return cardCollection.getName();
        } else if (isForCard()) {
            return card.getName();
        }
        return "Unknown";
    }

    public Integer getTargetId() {
        if (isForCollection()) {
            return cardCollection.getId();
        } else if (isForCard()) {
            return card.getId();
        }
        return null;
    }

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public CardCollection getCardCollection() {
        return cardCollection;
    }

    public void setCardCollection(CardCollection cardCollection) {
        this.cardCollection = cardCollection;
    }

    public Card getCard() {
        return card;
    }

    public void setCard(Card card) {
        this.card = card;
    }

    public RewardType getRewardType() {
        return rewardType;
    }

    public void setRewardType(RewardType rewardType) {
        this.rewardType = rewardType;
    }

    public Byte getMilestonePercentage() {
        return milestonePercentage;
    }

    public void setMilestonePercentage(Byte milestonePercentage) {
        this.milestonePercentage = milestonePercentage;
    }

    public JsonNode getRewardData() {
        return rewardData;
    }

    public void setRewardData(JsonNode rewardData) {
        this.rewardData = rewardData;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Reward reward = (Reward) o;
        return Objects.equals(id, reward.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Reward{" +
                "id=" + id +
                ", rewardType=" + rewardType +
                ", milestonePercentage=" + milestonePercentage +
                ", target=" + getTargetName() +
                '}';
    }
}
