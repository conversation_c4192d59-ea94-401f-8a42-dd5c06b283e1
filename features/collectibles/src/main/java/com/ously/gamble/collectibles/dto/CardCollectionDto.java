package com.ously.gamble.collectibles.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;

import java.time.LocalDateTime;
import java.util.List;

public class CardCollectionDto {

    public record CreateCardCollectionRequest(
            String name,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Integer sortOrder
    ) {}

    public record UpdateCardCollectionRequest(
            String name,
            LocalDateTime startDate,
            LocalDateTime endDate,
            CardCollection.CollectionStatus status,
            Integer sortOrder
    ) {}

    public record CreateCardRequest(
            String name,
            String imageUrl,
            Byte rarityLevel,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Byte sortOrder
    ) {}

    public record UpdateCardRequest(
            String name,
            String imageUrl,
            Byte rarityLevel,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Card.CardStatus status,
            Byte sortOrder
    ) {}

    public record CreateRewardRequest(
            Integer cardId, // null for collection level reward
            RewardType rewardType,
            Byte milestonePercentage,
            JsonNode rewardData
    ) {
        public enum RewardType {
            COMPLETION, MILESTONE
        }
    }

    public record UpdateRewardRequest(
            Byte milestonePercentage,
            JsonNode rewardData
    ) {}

    // Response DTOs
    public record CardCollectionResponse(
            Integer id,
            String name,
            String description,
            LocalDateTime startDate,
            LocalDateTime endDate,
            CardCollection.CollectionStatus status,
            Integer sortOrder,
            List<CardResponse> cards,
            List<RewardResponse> rewards
    ) {}

    public record CardResponse(
            Integer id,
            String name,
            String imageUrl,
            Byte rarityLevel,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Card.CardStatus status,
            Byte sortOrder,
            List<RewardResponse> rewards
    ) {}

    public record RewardResponse(
            Integer id,
            RewardType rewardType,
            Byte milestonePercentage,
            JsonNode rewardData,
            String targetType, // "COLLECTION" or "CARD"
            Integer targetId,
            String targetName
    ) {
        // TODO смущает дубликат енума тут и в энтити
        public enum RewardType {
            COMPLETION, MILESTONE
        }
    }

    public record CardCollectionSummaryResponse(
            Integer id,
            String name,
            String description,
            CardCollection.CollectionStatus status,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Integer totalCards,
            Integer totalRewards,
            boolean isActive,
            boolean isExpired
    ) {}

    // Filter request DTO
    public record CollectionFilterRequest(
            CardCollection.CollectionStatus status,
            Boolean activeOnly,
            Boolean includeExpired,
            LocalDateTime startDateFrom,
            LocalDateTime startDateTo,
            LocalDateTime endDateFrom,
            LocalDateTime endDateTo
    ) {}
}
