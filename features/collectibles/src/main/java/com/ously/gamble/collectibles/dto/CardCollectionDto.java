package com.ously.gamble.collectibles.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;
import java.util.List;

public class CardCollectionDto {

    //---------------------
    // Request DTOs
    //---------------------

    public record CreateCardCollectionRequest(
            @NotBlank(message = "Collection name cannot be empty")
            @Size(max = 100, message = "Collection name cannot exceed 100 characters")
            String name,

            LocalDateTime startDate,

            LocalDateTime endDate,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Integer sortOrder
    ) {
        public CreateCardCollectionRequest {
            if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
                throw new IllegalArgumentException("End date cannot be before start date");
            }
        }
    }

    public record UpdateCardCollectionRequest(
            @Size(max = 100, message = "Collection name cannot exceed 100 characters")
            String name,

            LocalDateTime startDate,
            LocalDateTime endDate,
            CardCollection.CollectionStatus status,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Integer sortOrder
    ) {
        public UpdateCardCollectionRequest {
            if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
                throw new IllegalArgumentException("End date cannot be before start date");
            }
        }
    }

    public record CreateCardRequest(
            @NotBlank(message = "Card name cannot be empty")
            @Size(max = 100, message = "Card name cannot exceed 100 characters")
            String name,

            @NotBlank(message = "Image URL is required")
            @Pattern(regexp = "^https?://.*", message = "Image URL must start with http:// or https://")
            String imageUrl,

            @NotNull(message = "Rarity level is required")
            @Min(value = 1, message = "Rarity level must be between 1 and 3")
            @Max(value = 3, message = "Rarity level must be between 1 and 3")
            Byte rarityLevel,

            LocalDateTime startDate,
            LocalDateTime endDate,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Byte sortOrder
    ) {
        public CreateCardRequest {
            if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
                throw new IllegalArgumentException("End date cannot be before start date");
            }
        }
    }

    public record UpdateCardRequest(
            @Size(max = 100, message = "Card name cannot exceed 100 characters")
            String name,

            @Pattern(regexp = "^https?://.*", message = "Image URL must start with http:// or https://")
            String imageUrl,

            @Min(value = 1, message = "Rarity level must be between 1 and 3")
            @Max(value = 3, message = "Rarity level must be between 1 and 3")
            Byte rarityLevel,

            LocalDateTime startDate,
            LocalDateTime endDate,
            Card.CardStatus status,

            @Min(value = 0, message = "Sort order cannot be negative")
            @Max(value = 99, message = "Sort order cannot exceed 99")
            Byte sortOrder
    ) {
        public UpdateCardRequest {
            if (endDate != null && startDate != null && endDate.isBefore(startDate)) {
                throw new IllegalArgumentException("End date cannot be before start date");
            }
        }
    }

    public record CreateRewardRequest(
            Integer collectionId,
            Integer cardId,

            @NotNull(message = "Reward type is required")
            Reward.RewardType rewardType,

            @Min(value = 1, message = "Milestone percentage must be between 1 and 100")
            @Max(value = 100, message = "Milestone percentage must be between 1 and 100")
            Byte milestonePercentage,

            @NotNull(message = "Reward data is required")
            JsonNode rewardData
    ) {
        public CreateRewardRequest {
            if (rewardType == Reward.RewardType.MILESTONE && milestonePercentage == null) {
                throw new IllegalArgumentException("Milestone percentage is required for milestone rewards");
            }
            if (rewardType == Reward.RewardType.COMPLETION && milestonePercentage != null) {
                throw new IllegalArgumentException("Milestone percentage should not be specified for completion rewards");
            }
        }
    }

    public record UpdateRewardRequest(
            @Min(value = 1, message = "Milestone percentage must be between 1 and 100")
            @Max(value = 100, message = "Milestone percentage must be between 1 and 100")
            Byte milestonePercentage,

            JsonNode rewardData
    ) {}

    //---------------------
    // Response DTOs
    //---------------------

    public record CardCollectionResponse(
            Integer id,
            String name,
            String description,
            LocalDateTime startDate,
            LocalDateTime endDate,
            CardCollection.CollectionStatus status,
            Integer sortOrder,
            List<CardResponse> cards,
            List<RewardResponse> rewards
    ) {}

    public record CardResponse(
            Integer id,
            String name,
            String imageUrl,
            Byte rarityLevel,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Card.CardStatus status,
            Byte sortOrder,
            List<RewardResponse> rewards
    ) {}

    public record RewardResponse(
            Integer id,
            Reward.RewardType rewardType,
            Byte milestonePercentage,
            JsonNode rewardData,
            String targetType,
            Integer targetId,
            String targetName
    ) {}

    public record CardCollectionSummaryResponse(
            Integer id,
            String name,
            String description,
            CardCollection.CollectionStatus status,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Integer totalCards,
            Integer totalRewards,
            boolean isActive,
            boolean isExpired
    ) {}

    public record CollectionFilterRequest(
            CardCollection.CollectionStatus status,
            Boolean activeOnly,
            Boolean includeExpired,
            LocalDateTime startDateFrom,
            LocalDateTime startDateTo,
            LocalDateTime endDateFrom,
            LocalDateTime endDateTo
    ) {}
}
