package com.ously.gamble.collectibles.service;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.persistence.model.Reward;
import com.ously.gamble.collectibles.persistence.repository.CardCollectionRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class CardCollectionService {

    private final CardCollectionRepository cardCollectionRepository;

    public CardCollectionService(CardCollectionRepository cardCollectionRepository) {
        this.cardCollectionRepository = cardCollectionRepository;
    }

    // === READ OPERATIONS ===

    @Transactional(readOnly = true)
    public List<CardCollection> findAll() {
        return cardCollectionRepository.findByOrderBySortOrderAscCreatedAtAsc();
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findAll(Pageable pageable) {
        return cardCollectionRepository.findAll(pageable);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findById(Integer id) {
        return cardCollectionRepository.findById(id);
    }

    @Transactional(readOnly = true)
    public Optional<CardCollection> findByIdWithCardsAndRewards(Integer id) {
        return Optional.ofNullable(cardCollectionRepository.findByIdWithCardsAndRewards(id));
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findActiveCollections() {
        return cardCollectionRepository.findActiveCollections(LocalDateTime.now());
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findActiveCollections(Pageable pageable) {
        return cardCollectionRepository.findActiveCollections(LocalDateTime.now(), pageable);
    }

    @Transactional(readOnly = true)
    public List<CardCollection> findByStatus(CardCollection.CollectionStatus status) {
        return cardCollectionRepository.findByStatus(status);
    }

    @Transactional(readOnly = true)
    public Page<CardCollection> findByStatus(CardCollection.CollectionStatus status, Pageable pageable) {
        return cardCollectionRepository.findByStatus(status, pageable);
    }

    // === COLLECTION MANAGEMENT ===

    public CardCollection create(CardCollectionDto.CreateCardCollectionRequest request) {
        validateCreateRequest(request);
        
        CardCollection collection = new CardCollection();
        collection.setName(request.name());
        collection.setDescription(request.description());
        
        if (request.startDate() != null) {
            collection.setStartDate(request.startDate());
        }
        if (request.endDate() != null) {
            validateDateRange(request.startDate(), request.endDate());
            collection.setEndDate(request.endDate());
        }
        if (request.sortOrder() != null) {
            collection.setSortOrder(request.sortOrder());
        }
        
        return cardCollectionRepository.save(collection);
    }

    public Optional<CardCollection> update(Integer id, CardCollectionDto.UpdateCardCollectionRequest request) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    if (request.name() != null) {
                        validateCollectionName(request.name(), id);
                        collection.setName(request.name());
                    }
                    if (request.description() != null) {
                        collection.setDescription(request.description());
                    }
                    if (request.startDate() != null) {
                        LocalDateTime endDate = request.endDate() != null ? request.endDate() : collection.getEndDate();
                        validateDateRange(request.startDate(), endDate);
                        collection.setStartDate(request.startDate());
                    }
                    if (request.endDate() != null) {
                        validateDateRange(collection.getStartDate(), request.endDate());
                        collection.setEndDate(request.endDate());
                    }
                    if (request.status() != null) {
                        validateStatusChange(collection, request.status());
                        collection.setStatus(request.status());
                    }
                    if (request.sortOrder() != null) {
                        collection.setSortOrder(request.sortOrder());
                    }
                    return cardCollectionRepository.save(collection);
                });
    }

    public boolean delete(Integer id) {
        Optional<CardCollection> collection = cardCollectionRepository.findById(id);
        if (collection.isPresent()) {
            validateCollectionDeletion(collection.get());
            cardCollectionRepository.deleteById(id);
            return true;
        }
        return false;
    }

    public Optional<CardCollection> activate(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    validateCanActivate(collection);
                    collection.setStatus(CardCollection.CollectionStatus.ENABLED);
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> disable(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.setStatus(CardCollection.CollectionStatus.DISABLED);
                    return cardCollectionRepository.save(collection);
                });
    }

    public Optional<CardCollection> expire(Integer id) {
        return cardCollectionRepository.findById(id)
                .map(collection -> {
                    collection.setStatus(CardCollection.CollectionStatus.EXPIRED);
                    return cardCollectionRepository.save(collection);
                });
    }

    public boolean delete(Integer id) {
        if (cardCollectionRepository.existsById(id)) {
            cardCollectionRepository.deleteById(id);
            return true;
        }
        return false;
    }

    // === CARD MANAGEMENT ===

    public CardCollection createCard(Integer collectionId, CardCollectionDto.CreateCardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
        
        validateCardCreation(collection, request);
        
        Card card = new Card();
        card.setCardCollection(collection);
        card.setName(request.name());
        card.setImageUrl(request.imageUrl());
        card.setRarityLevel(request.rarityLevel());
        card.setStatus(Card.CardStatus.DISABLED);
        
        if (request.startDate() != null) {
            card.setStartDate(request.startDate());
        }
        if (request.endDate() != null) {
            card.setEndDate(request.endDate());
        }
        if (request.sortOrder() != null) {
            card.setSortOrder(request.sortOrder());
        }
        
        collection.getCards().add(card);
        return cardCollectionRepository.save(collection);
    }

    public CardCollection updateCard(Integer collectionId, Integer cardId, CardCollectionDto.UpdateCardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
        
        Card card = findCardInCollection(collection, cardId);
        validateCardUpdate(collection, card, request);
        
        if (request.name() != null) {
            card.setName(request.name());
        }
        if (request.imageUrl() != null) {
            card.setImageUrl(request.imageUrl());
        }
        if (request.rarityLevel() != null) {
            card.setRarityLevel(request.rarityLevel());
        }
        if (request.startDate() != null) {
            card.setStartDate(request.startDate());
        }
        if (request.endDate() != null) {
            card.setEndDate(request.endDate());
        }
        if (request.status() != null) {
            card.setStatus(request.status());
        }
        if (request.sortOrder() != null) {
            card.setSortOrder(request.sortOrder());
        }
        
        return cardCollectionRepository.save(collection);
    }

    public CardCollection deleteCard(Integer collectionId, Integer cardId) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
        
        Card card = findCardInCollection(collection, cardId);
        validateCardDeletion(collection, card);
        
        collection.getCards().remove(card);
        return cardCollectionRepository.save(collection);
    }

    // === REWARD MANAGEMENT ===

    public CardCollection createReward(Integer collectionId, CardCollectionDto.CreateRewardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
        
        validateRewardCreation(collection, request);
        
        Reward reward = new Reward();
        reward.setRewardType(mapRewardType(request.rewardType()));
        reward.setRewardData(request.rewardData());
        
        if (request.cardId() != null) {
            Card card = findCardInCollection(collection, request.cardId());
            reward.setCard(card);
        } else {
            reward.setCardCollection(collection);
        }
        
        if (request.rewardType() == CardCollectionDto.CreateRewardRequest.RewardType.MILESTONE) {
            validateMilestonePercentage(request.milestonePercentage());
            reward.setMilestonePercentage(request.milestonePercentage());
        }
        
        collection.getRewards().add(reward);
        return cardCollectionRepository.save(collection);
    }

    public CardCollection updateReward(Integer collectionId, Integer rewardId, CardCollectionDto.UpdateRewardRequest request) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
        
        Reward reward = findRewardInCollection(collection, rewardId);
        
        if (request.rewardData() != null) {
            reward.setRewardData(request.rewardData());
        }
        if (request.milestonePercentage() != null) {
            validateMilestonePercentage(request.milestonePercentage());
            reward.setMilestonePercentage(request.milestonePercentage());
        }
        
        return cardCollectionRepository.save(collection);
    }

    public CardCollection deleteReward(Integer collectionId, Integer rewardId) {
        CardCollection collection = cardCollectionRepository.findById(collectionId)
                .orElseThrow(() -> new IllegalArgumentException("Collection not found: " + collectionId));
        
        Reward reward = findRewardInCollection(collection, rewardId);
        collection.getRewards().remove(reward);
        
        return cardCollectionRepository.save(collection);
    }

    // === VALIDATION METHODS ===

    private void validateCreateRequest(CardCollectionDto.CreateCardCollectionRequest request) {
        if (request.name() == null || request.name().trim().isEmpty()) {
            throw new IllegalArgumentException("Collection name is required");
        }
        validateCollectionName(request.name(), null);
        if (request.startDate() != null && request.endDate() != null) {
            validateDateRange(request.startDate(), request.endDate());
        }
    }

    private void validateCollectionName(String name, Integer excludeId) {
        boolean exists = cardCollectionRepository.findAll().stream()
                .anyMatch(c -> c.getName().equals(name) && !c.getId().equals(excludeId));
        if (exists) {
            throw new IllegalArgumentException("Collection name already exists: " + name);
        }
    }

    private void validateDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        if (startDate != null && endDate != null && startDate.isAfter(endDate)) {
            throw new IllegalArgumentException("Start date cannot be after end date");
        }
    }

    private void validateStatusChange(CardCollection collection, CardCollection.CollectionStatus newStatus) {
        if (newStatus == CardCollection.CollectionStatus.ENABLED) {
            validateCanActivate(collection);
        }
    }

    private void validateCanActivate(CardCollection collection) {
        if (collection.getCards().isEmpty()) {
            throw new IllegalStateException("Cannot activate collection without cards");
        }
    }

    private void validateCollectionDeletion(CardCollection collection) {
        if (collection.getStatus() == CardCollection.CollectionStatus.ENABLED) {
            throw new IllegalStateException("Cannot delete active collection");
        }
    }

    private void validateCardCreation(CardCollection collection, CardCollectionDto.CreateCardRequest request) {
        if (request.name() == null || request.name().trim().isEmpty()) {
            throw new IllegalArgumentException("Card name is required");
        }
        
        boolean nameExists = collection.getCards().stream()
                .anyMatch(card -> card.getName().equals(request.name()));
        if (nameExists) {
            throw new IllegalArgumentException("Card name already exists in collection: " + request.name());
        }
        
        if (request.rarityLevel() == null || request.rarityLevel() < 1 || request.rarityLevel() > 3) {
            throw new IllegalArgumentException("Rarity level must be between 1 and 3");
        }
    }

    private void validateCardUpdate(CardCollection collection, Card card, CardCollectionDto.UpdateCardRequest request) {
        if (request.name() != null) {
            boolean nameExists = collection.getCards().stream()
                    .anyMatch(c -> c.getName().equals(request.name()) && !c.getId().equals(card.getId()));
            if (nameExists) {
                throw new IllegalArgumentException("Card name already exists in collection: " + request.name());
            }
        }
    }

    private void validateCardDeletion(CardCollection collection, Card card) {
        if (collection.getCards().size() <= 1) {
            throw new IllegalStateException("Collection must have at least one card");
        }
    }

    private void validateRewardCreation(CardCollection collection, CardCollectionDto.CreateRewardRequest request) {
        if (request.rewardData() == null) {
            throw new IllegalArgumentException("Reward data is required");
        }
        if (request.rewardType() == CardCollectionDto.CreateRewardRequest.RewardType.MILESTONE 
            && request.milestonePercentage() == null) {
            throw new IllegalArgumentException("Milestone percentage is required for milestone rewards");
        }
    }

    private void validateMilestonePercentage(Byte percentage) {
        if (percentage == null || percentage < 1 || percentage > 100) {
            throw new IllegalArgumentException("Milestone percentage must be between 1 and 100");
        }
        if (percentage % 5 != 0) {
            throw new IllegalArgumentException("Milestone percentage should be in 5% intervals");
        }
    }

    // === HELPER METHODS ===

    private Card findCardInCollection(CardCollection collection, Integer cardId) {
        return collection.getCards().stream()
                .filter(card -> card.getId().equals(cardId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Card not found in collection: " + cardId));
    }

    private Reward findRewardInCollection(CardCollection collection, Integer rewardId) {
        return collection.getRewards().stream()
                .filter(reward -> reward.getId().equals(rewardId))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Reward not found in collection: " + rewardId));
    }

    private Reward.RewardType mapRewardType(CardCollectionDto.CreateRewardRequest.RewardType rewardType) {
        return switch (rewardType) {
            case COMPLETION -> Reward.RewardType.COMPLETION;
            case MILESTONE -> Reward.RewardType.MILESTONE;
        };
    }
}
