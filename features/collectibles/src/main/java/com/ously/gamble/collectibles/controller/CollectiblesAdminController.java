package com.ously.gamble.collectibles.controller;

import com.ously.gamble.collectibles.dto.CardCollectionDto;
import com.ously.gamble.collectibles.dto.CardCollectionMapper;
import com.ously.gamble.collectibles.persistence.model.Card;
import com.ously.gamble.collectibles.persistence.model.CardCollection;
import com.ously.gamble.collectibles.service.CardCollectionService;
import com.ously.gamble.collectibles.service.CardService;
import com.ously.gamble.collectibles.service.RewardService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.security.RolesAllowed;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin/collectibles")
@Tag(name = "Collectibles Admin", description = "Admin operations for card collections and cards")
public class CollectiblesAdminController {

    private final CardCollectionService cardCollectionService;
    private final CardService cardService;
    private final RewardService rewardService;
    private final CardCollectionMapper mapper;

    public CollectiblesAdminController(
            CardCollectionService cardCollectionService,
            CardService cardService,
            RewardService rewardService,
            CardCollectionMapper mapper) {
        this.cardCollectionService = cardCollectionService;
        this.cardService = cardService;
        this.rewardService = rewardService;
        this.mapper = mapper;
    }

    // === COLLECTION OPERATIONS ===

    @Operation(description = "Get all collections with filters", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collections")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardCollectionSummaryResponse> getCollections(
            @RequestParam(required = false) CardCollection.CollectionStatus status,
            @RequestParam(required = false, defaultValue = "false") boolean activeOnly,
            @RequestParam(required = false, defaultValue = "false") boolean detailed) {
        
        List<CardCollection> collections;
        
        if (activeOnly) {
            collections = cardCollectionService.findActiveCollections();
        } else if (status != null) {
            collections = cardCollectionService.findByStatus(status);
        } else {
            collections = cardCollectionService.findAll();
        }
        
        return mapper.toSummaryResponseList(collections);
    }

    @Operation(description = "Get collections with pagination", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collections/paged")
    @RolesAllowed("ADMIN")
    public Page<CardCollectionDto.CardCollectionSummaryResponse> getCollectionsPaged(
            @PageableDefault(size = 20) Pageable pageable,
            @RequestParam(required = false) CardCollection.CollectionStatus status,
            @RequestParam(required = false, defaultValue = "false") boolean activeOnly) {
        
        Page<CardCollection> collections;
        
        if (activeOnly) {
            collections = cardCollectionService.findActiveCollections(pageable);
        } else if (status != null) {
            collections = cardCollectionService.findByStatus(status, pageable);
        } else {
            collections = cardCollectionService.findAll(pageable);
        }
        
        return collections.map(mapper::toSummaryResponse);
    }

    @Operation(description = "Get collection details", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/collections/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> getCollection(@PathVariable Integer id) {
        return cardCollectionService.findByIdWithCardsAndRewards(id)
                .map(collection -> ResponseEntity.ok(mapper.toResponse(collection)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/collections")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createCollection(
            @RequestBody CardCollectionDto.CreateCardCollectionRequest request) {
        CardCollection collection = cardCollectionService.create(request);
        CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
        return mapper.toResponse(fullCollection);
    }

    @Operation(description = "Update collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/collections/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> updateCollection(
            @PathVariable Integer id,
            @RequestBody CardCollectionDto.UpdateCardCollectionRequest request) {
        return cardCollectionService.update(id, request)
                .map(collection -> {
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Delete collection", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/collections/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<Void> deleteCollection(@PathVariable Integer id) {
        if (cardCollectionService.delete(id)) {
            return ResponseEntity.noContent().build();
        }
        return ResponseEntity.notFound().build();
    }

    @Operation(description = "Activate collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/collections/{id}/activate")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> activateCollection(@PathVariable Integer id) {
        return cardCollectionService.activate(id)
                .map(collection -> {
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Disable collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/collections/{id}/disable")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> disableCollection(@PathVariable Integer id) {
        return cardCollectionService.disable(id)
                .map(collection -> {
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Expire collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/collections/{id}/expire")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardCollectionResponse> expireCollection(@PathVariable Integer id) {
        return cardCollectionService.expire(id)
                .map(collection -> {
                    CardCollection fullCollection = cardCollectionService.findByIdWithCardsAndRewards(collection.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toResponse(fullCollection));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // === CARD OPERATIONS ===

    @Operation(description = "Get all cards with filters", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/cards")
    @RolesAllowed("ADMIN")
    public List<CardCollectionDto.CardResponse> getCards(
            @RequestParam(required = false) Card.CardStatus status,
            @RequestParam(required = false) Byte rarityLevel,
            @RequestParam(required = false) Integer collectionId,
            @RequestParam(required = false, defaultValue = "false") boolean activeOnly) {
        
        List<Card> cards;
        
        if (activeOnly) {
            cards = cardService.findActiveCards();
        } else if (status != null) {
            cards = cardService.findByStatus(status);
        } else if (rarityLevel != null) {
            cards = cardService.findByRarityLevel(rarityLevel);
        } else if (collectionId != null) {
            cards = cardService.findByCollectionId(collectionId);
        } else {
            cards = cardService.findAll();
        }
        
        return mapper.toCardResponseList(cards);
    }

    @Operation(description = "Get card details", security = {@SecurityRequirement(name = "bearer-key")})
    @GetMapping("/cards/{id}")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> getCard(@PathVariable Integer id) {
        return cardService.findByIdWithRewards(id)
                .map(card -> ResponseEntity.ok(mapper.toCardResponse(card)))
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Create card in collection", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/collections/{collectionId}/cards")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createCard(
            @PathVariable Integer collectionId,
            @RequestBody CardCollectionDto.CreateCardRequest request) {
        CardCollection collection = cardCollectionService.createCard(collectionId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Update card", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/collections/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse updateCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId,
            @RequestBody CardCollectionDto.UpdateCardRequest request) {
        CardCollection collection = cardCollectionService.updateCard(collectionId, cardId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Delete card", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/collections/{collectionId}/cards/{cardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse deleteCard(
            @PathVariable Integer collectionId,
            @PathVariable Integer cardId) {
        CardCollection collection = cardCollectionService.deleteCard(collectionId, cardId);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Activate card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/cards/{id}/activate")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> activateCard(@PathVariable Integer id) {
        return cardService.activate(id)
                .map(card -> {
                    Card fullCard = cardService.findByIdWithRewards(card.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toCardResponse(fullCard));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Disable card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/cards/{id}/disable")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> disableCard(@PathVariable Integer id) {
        return cardService.disable(id)
                .map(card -> {
                    Card fullCard = cardService.findByIdWithRewards(card.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toCardResponse(fullCard));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(description = "Expire card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/cards/{id}/expire")
    @RolesAllowed("ADMIN")
    public ResponseEntity<CardCollectionDto.CardResponse> expireCard(@PathVariable Integer id) {
        return cardService.expire(id)
                .map(card -> {
                    Card fullCard = cardService.findByIdWithRewards(card.getId()).orElseThrow();
                    return ResponseEntity.ok(mapper.toCardResponse(fullCard));
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // === REWARD OPERATIONS ===

    @Operation(description = "Create reward for collection or card", security = {@SecurityRequirement(name = "bearer-key")})
    @PostMapping("/collections/{collectionId}/rewards")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse createReward(
            @PathVariable Integer collectionId,
            @RequestBody CardCollectionDto.CreateRewardRequest request) {
        CardCollection collection = cardCollectionService.createReward(collectionId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Update reward", security = {@SecurityRequirement(name = "bearer-key")})
    @PutMapping("/collections/{collectionId}/rewards/{rewardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse updateReward(
            @PathVariable Integer collectionId,
            @PathVariable Integer rewardId,
            @RequestBody CardCollectionDto.UpdateRewardRequest request) {
        CardCollection collection = cardCollectionService.updateReward(collectionId, rewardId, request);
        return mapper.toResponse(collection);
    }

    @Operation(description = "Delete reward", security = {@SecurityRequirement(name = "bearer-key")})
    @DeleteMapping("/collections/{collectionId}/rewards/{rewardId}")
    @RolesAllowed("ADMIN")
    public CardCollectionDto.CardCollectionResponse deleteReward(
            @PathVariable Integer collectionId,
            @PathVariable Integer rewardId) {
        CardCollection collection = cardCollectionService.deleteReward(collectionId, rewardId);
        return mapper.toResponse(collection);
    }
}
